# Lark机器人服务器重启系统 - 共识文档

## 技术选型确认

### 核心技术栈
- **后端框架**: FastAPI (异步支持，高性能)
- **前端框架**: 独立前端应用 (前后端分离架构)
- **认证方式**: JWT Token认证 (无状态，适合API)
- **任务队列**: Celery + Redis (轻量级消息队列)
- **数据库**: MySQL 8.0 + SQLAlchemy ORM
- **数据库迁移**: Alembic
- **配置管理**: config.py + .env文件

### 性能与部署规格
- **部署架构**: 前后端分离部署
- **后端部署**: FastAPI应用独立部署
- **前端部署**: 静态资源独立部署
- **并发支持**: 100+ 用户并发
- **数据库连接池**: 最大50个连接
- **安全要求**: 支持CORS跨域，JWT认证
- **日志策略**: 按天保存，暂不清理

### 定时任务配置
- **同步频率**: 在config.py中配置，支持动态调整
- **默认频率**: 每小时同步一次服务器信息
- **手动触发**: 支持API接口手动触发同步

## 功能需求确认

### 核心业务流程
1. **服务器信息同步**: 定时获取Vultr裸机实例列表并入库
2. **用户认证**: Web登录页面，用户名+密码，JWT Token管理
3. **消息处理**: 接收Lark机器人消息，解析服务器IP/ID
4. **权限验证**: 验证Lark用户与服务器绑定关系
5. **重启操作**: 调用Vultr API重启服务器
6. **状态监控**: 轮询6次服务器重启状态，每次间隔10秒
7. **日志记录**: 记录所有操作日志到数据库
8. **前端管理界面**: 独立的前端应用，提供服务器绑定关系维护功能

### 数据模型设计
1. **用户表(users)**: Web登录用户信息
   - id, username, password_hash, created_at, updated_at
2. **服务器表(servers)**: Vultr裸机实例信息
   - id, vultr_id, ip_address, label, status, region, created_at, updated_at
3. **用户服务器绑定表(user_server_bindings)**: Lark用户与服务器关系
   - id, lark_user_id, server_id, created_at, updated_at
4. **操作日志表(operation_logs)**: 重启操作记录
   - id, lark_user_id, server_id, operation_type, status, result, error_message, created_at

### API接口设计

#### 后端API接口 (供前端调用)
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - Token刷新
- `GET /api/servers` - 获取服务器列表
- `POST /api/servers/sync` - 手动同步服务器信息
- `GET /api/bindings` - 获取绑定关系
- `POST /api/bindings` - 创建绑定关系
- `DELETE /api/bindings/{id}` - 删除绑定关系
- `GET /api/logs` - 获取操作日志

#### Lark机器人接口
- `POST /webhook/lark` - 统一Webhook接口

### 三方接口集成

#### Vultr API
- `GET /v2/bare-metals` - 列出裸机实例
- `POST /v2/bare-metals/{baremetal-id}/reboot` - 重启裸机实例
- `GET /v2/bare-metals/{baremetal-id}` - 获取裸机实例详情

#### Lark API
- 事件订阅接口 - 接收消息事件
- 消息回复接口 - 发送回复消息
- 用户信息接口 - 获取用户详情

## 技术实现方案

### 项目结构设计
```
lark_robot/
├── backend/                   # 后端Python代码
│   ├── __init__.py
│   ├── main.py                # FastAPI应用入口
│   ├── config.py              # 配置管理
│   ├── database.py            # 数据库连接
│   ├── models/                # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── server.py
│   │   ├── binding.py
│   │   └── log.py
│   ├── schemas/               # Pydantic模型
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── server.py
│   │   └── log.py
│   ├── api/                   # API路由
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── server.py
│   │   ├── binding.py
│   │   ├── log.py
│   │   └── lark.py
│   ├── services/              # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── server_service.py
│   │   ├── lark_service.py
│   │   └── vultr_service.py
│   ├── tasks/                 # Celery任务
│   │   ├── __init__.py
│   │   ├── server_sync.py
│   │   └── restart_monitor.py
│   ├── utils/                 # 工具函数
│   │   ├── __init__.py
│   │   ├── jwt_utils.py
│   │   ├── logger.py
│   │   └── validators.py
│   └── tests/                 # 后端测试文件
│   ├── alembic/               # 数据库迁移
│   ├── requirements.txt       # 后端依赖包
│   └── .env                   # 后端环境变量
├── frontend/                  # 前端代码
│   ├── src/
│   │   ├── pages/             # 页面文件
│   │   ├── js/                # JavaScript文件
│   │   ├── css/               # 样式文件
│   │   └── assets/            # 静态资源
│   ├── package.json           # 前端依赖配置
│   ├── .env                   # 前端环境变量
│   └── README.md              # 前端项目说明
├── .gitignore
└── README.md                  # 项目总体说明



### 环境配置(.env)
```
# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/lark_robot

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Vultr API
VULTR_API_KEY=your-vultr-api-key
VULTR_API_BASE_URL=https://api.vultr.com

# Lark配置
LARK_APP_ID=your-lark-app-id
LARK_APP_SECRET=your-lark-app-secret
LARK_ENCRYPT_KEY=your-lark-encrypt-key
LARK_VERIFY_TOKEN=your-lark-verify-token

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
```

### 系统配置(config.py)
```python
# 定时任务配置
SERVER_SYNC_INTERVAL_HOURS = 1  # 服务器信息同步间隔(小时)
RESTART_MONITOR_INTERVAL_SECONDS = 10  # 重启状态监控间隔(秒)
RESTART_MAX_RETRIES = 6  # 重启状态检查最大次数

# 数据库连接池配置
DB_POOL_SIZE = 20
DB_MAX_OVERFLOW = 30
DB_POOL_TIMEOUT = 30

# 并发配置
MAX_WORKERS = 10
API_RATE_LIMIT = 100  # 每分钟请求限制
```

## 验收标准

### 功能验收
1. **用户认证**: Web登录正常，JWT Token生成和验证正确
2. **服务器同步**: 定时任务正常运行，服务器信息准确入库
3. **消息处理**: Lark机器人正确解析IP/ID，回复消息准确
4. **权限验证**: 绑定关系验证正确，非法操作被拦截
5. **重启功能**: 服务器重启成功，状态监控准确
6. **日志记录**: 所有操作日志完整记录
7. **Web管理**: 绑定关系管理功能正常

### 性能验收
1. **并发处理**: 支持100+用户同时使用
2. **响应时间**: API响应时间<500ms
3. **数据库性能**: 连接池配置合理，无连接泄漏
4. **任务处理**: Celery任务正常执行，无积压

### 安全验收
1. **敏感信息**: 所有API密钥存储在.env文件
2. **权限控制**: 用户只能操作绑定的服务器
3. **输入验证**: 所有用户输入经过验证
4. **错误处理**: 异常情况有适当的错误处理

### 代码质量验收
1. **代码规范**: 遵循PEP8和项目编码规范
2. **测试覆盖**: 核心功能有单元测试
3. **文档完整**: API文档、部署文档齐全
4. **可维护性**: 代码结构清晰，易于扩展

## 交付清单

### 代码交付
- [ ] 完整的FastAPI后端应用代码 (backend/)
- [ ] 数据库模型和迁移脚本
- [ ] Celery任务定义
- [ ] 独立前端管理界面项目 (frontend/)
- [ ] 后端单元测试代码
- [ ] 前端功能测试

### 文档交付
- [ ] API接口文档
- [ ] 部署安装文档
- [ ] 用户使用手册
- [ ] 系统架构文档

### 配置交付
- [ ] 环境配置模板(.env.example)
- [ ] 数据库初始化脚本
- [ ] 系统配置文件
- [ ] 依赖包清单(requirements.txt)