// 仪表板组件 - 服务器管理
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, <PERSON><PERSON>, Alert, Spin<PERSON>, Badge } from 'react-bootstrap';
import { apiService, Server } from '../services/api';

const Dashboard: React.FC = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    loadServers();
  }, []);

  const loadServers = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await apiService.getServers();
      setServers(response.items);
    } catch (error: any) {
      console.error('加载服务器列表失败:', error);
      setError('加载服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    try {
      setSyncing(true);
      setError('');
      setSuccess('');
      
      const response = await apiService.syncServers();
      setSuccess(`同步成功，共同步 ${response.synced_count} 台服务器`);
      
      // 重新加载服务器列表
      await loadServers();
    } catch (error: any) {
      console.error('同步服务器失败:', error);
      setError('同步服务器失败');
    } finally {
      setSyncing(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    const statusMap: { [key: string]: string } = {
      'active': 'success',
      'inactive': 'secondary',
      'pending': 'warning',
      'error': 'danger'
    };

    const variant = statusMap[status?.toLowerCase() || 'unknown'] || 'secondary';
    
    return (
      <Badge bg={variant}>
        {status || 'Unknown'}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h2>
              <i className="bi bi-server me-2"></i>
              服务器管理
            </h2>
            <Button
              variant="success"
              onClick={handleSync}
              disabled={syncing}
              className="d-flex align-items-center"
            >
              {syncing ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    className="me-2"
                  />
                  同步中...
                </>
              ) : (
                <>
                  <i className="bi bi-arrow-clockwise me-2"></i>
                  同步服务器
                </>
              )}
            </Button>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          <i className="bi bi-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          <i className="bi bi-check-circle me-2"></i>
          {success}
        </Alert>
      )}

      <Card className="shadow-sm">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">加载中...</span>
              </Spinner>
              <p className="mt-2 text-muted">加载服务器列表中...</p>
            </div>
          ) : servers.length === 0 ? (
            <div className="text-center py-5">
              <i className="bi bi-inbox display-1 text-muted"></i>
              <p className="mt-3 text-muted">暂无服务器数据</p>
              <Button variant="primary" onClick={handleSync}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                同步服务器
              </Button>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table striped hover>
                  <thead className="table-dark">
                    <tr>
                      <th>ID</th>
                      <th>Vultr ID</th>
                      <th>IP地址</th>
                      <th>标签</th>
                      <th>状态</th>
                      <th>区域</th>
                      <th>更新时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {servers.map((server) => (
                      <tr key={server.id}>
                        <td>{server.id}</td>
                        <td>
                          <code className="text-primary">{server.vultr_id}</code>
                        </td>
                        <td>{server.ip_address || '-'}</td>
                        <td>{server.label || '-'}</td>
                        <td>{getStatusBadge(server.status)}</td>
                        <td>{server.region || '-'}</td>
                        <td className="text-muted">
                          {formatDate(server.updated_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
              
              <div className="mt-3 text-muted">
                <small>
                  <i className="bi bi-info-circle me-1"></i>
                  共 {servers.length} 台服务器
                </small>
              </div>
            </>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default Dashboard;
