// 操作日志组件
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Al<PERSON>, Spin<PERSON>, Badge } from 'react-bootstrap';
import { apiService, OperationLog } from '../services/api';

const Logs: React.FC = () => {
  const [logs, setLogs] = useState<OperationLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadLogs();
  }, []);

  const loadLogs = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await apiService.getOperationLogs();
      setLogs(response.items);
    } catch (error: any) {
      console.error('加载操作日志失败:', error);
      setError('加载操作日志失败');
    } finally {
      setLoading(false);
    }
  };

  const getOperationTypeBadge = (type: string) => {
    const typeMap: { [key: string]: { variant: string; text: string } } = {
      'restart': { variant: 'primary', text: '重启' },
      'sync': { variant: 'info', text: '同步' }
    };

    const config = typeMap[type] || { variant: 'secondary', text: type };
    
    return (
      <Badge bg={config.variant}>
        {config.text}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusMap: { [key: string]: { variant: string; text: string } } = {
      'success': { variant: 'success', text: '成功' },
      'failed': { variant: 'danger', text: '失败' },
      'in_progress': { variant: 'warning', text: '进行中' }
    };

    const config = statusMap[status] || { variant: 'secondary', text: status };
    
    return (
      <Badge bg={config.variant}>
        {config.text}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <h2>
            <i className="bi bi-journal-text me-2"></i>
            操作日志
          </h2>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          <i className="bi bi-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      <Card className="shadow-sm">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">加载中...</span>
              </Spinner>
              <p className="mt-2 text-muted">加载操作日志中...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-5">
              <i className="bi bi-journal-text display-1 text-muted"></i>
              <p className="mt-3 text-muted">暂无操作日志</p>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table striped hover>
                  <thead className="table-dark">
                    <tr>
                      <th>ID</th>
                      <th>Lark用户ID</th>
                      <th>服务器</th>
                      <th>操作类型</th>
                      <th>状态</th>
                      <th>结果/错误信息</th>
                      <th>时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {logs.map((log) => (
                      <tr key={log.id}>
                        <td>{log.id}</td>
                        <td>
                          <code className="text-primary">{log.lark_user_id}</code>
                        </td>
                        <td>
                          {log.server ? (
                            <div>
                              <div className="fw-semibold">
                                {log.server.ip_address || log.server.vultr_id}
                              </div>
                              {log.server.label && (
                                <small className="text-muted">{log.server.label}</small>
                              )}
                            </div>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td>{getOperationTypeBadge(log.operation_type)}</td>
                        <td>{getStatusBadge(log.status)}</td>
                        <td>
                          <div style={{ maxWidth: '300px' }}>
                            {log.result && (
                              <div className="text-success small">
                                <i className="bi bi-check-circle me-1"></i>
                                {log.result}
                              </div>
                            )}
                            {log.error_message && (
                              <div className="text-danger small">
                                <i className="bi bi-exclamation-triangle me-1"></i>
                                {log.error_message}
                              </div>
                            )}
                            {!log.result && !log.error_message && '-'}
                          </div>
                        </td>
                        <td className="text-muted">
                          {formatDate(log.created_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
              
              <div className="mt-3 text-muted">
                <small>
                  <i className="bi bi-info-circle me-1"></i>
                  共 {logs.length} 条操作日志
                </small>
              </div>
            </>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default Logs;
