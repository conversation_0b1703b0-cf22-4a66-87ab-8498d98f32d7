// 导航栏组件
import React from 'react';
import { Navbar as BSNavbar, Nav, NavDropdown, Container } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <BSNavbar bg="primary" variant="dark" expand="lg" className="shadow-sm">
      <Container>
        <BSNavbar.Brand href="#" className="fw-bold">
          <i className="bi bi-robot me-2"></i>
          Lark机器人管理系统
        </BSNavbar.Brand>
        
        <BSNavbar.Toggle aria-controls="basic-navbar-nav" />
        
        <BSNavbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link 
              onClick={() => navigate('/dashboard')}
              className={isActive('/dashboard') ? 'active' : ''}
            >
              <i className="bi bi-server me-1"></i>
              服务器管理
            </Nav.Link>
            <Nav.Link 
              onClick={() => navigate('/bindings')}
              className={isActive('/bindings') ? 'active' : ''}
            >
              <i className="bi bi-link-45deg me-1"></i>
              绑定关系
            </Nav.Link>
            {/* 操作日志功能暂时隐藏 */}
            {/*
            <Nav.Link 
              onClick={() => navigate('/logs')}
              className={isActive('/logs') ? 'active' : ''}
            >
              <i className="bi bi-journal-text me-1"></i>
              操作日志
            </Nav.Link>
            */}
          </Nav>
          
          <Nav>
            <NavDropdown
              title={
                <>
                  <i className="bi bi-person-circle me-1"></i>
                  {user?.username || '用户'}
                </>
              }
              id="user-dropdown"
              align="end"
            >
              <NavDropdown.Item onClick={handleLogout}>
                <i className="bi bi-box-arrow-right me-2"></i>
                退出登录
              </NavDropdown.Item>
            </NavDropdown>
          </Nav>
        </BSNavbar.Collapse>
      </Container>
    </BSNavbar>
  );
};

export default Navbar;
