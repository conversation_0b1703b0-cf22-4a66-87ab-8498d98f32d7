// 绑定关系管理组件
import React, { useState, useEffect } from 'react';
import { 
  Container, Row, Col, Card, Table, Button, Alert, Spinner, 
  Modal, Form, InputGroup 
} from 'react-bootstrap';
import { apiService, UserServerBinding, Server, LarkUser } from '../services/api';

const Bindings: React.FC = () => {
  const [bindings, setBindings] = useState<UserServerBinding[]>([]);
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchEmail, setSearchEmail] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchedUser, setSearchedUser] = useState<LarkUser | null>(null);
  const [serverFilter, setServerFilter] = useState('');
  
  // 添加绑定模态框状态
  const [showAddModal, setShowAddModal] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [newBinding, setNewBinding] = useState({
    larkUserId: '',
    serverId: ''
  });

  useEffect(() => {
    loadBindings();
    loadServers();
  }, []);

  const loadBindings = async (larkUserId?: string) => {
    try {
      setLoading(true);
      setError('');
      const data = await apiService.getBindings(larkUserId);
      setBindings(data);
    } catch (error: any) {
      console.error('加载绑定关系失败:', error);
      setError('加载绑定关系失败');
    } finally {
      setLoading(false);
    }
  };

  const loadServers = async () => {
    try {
      const response = await apiService.getServers();
      setServers(response.items);
    } catch (error: any) {
      console.error('加载服务器列表失败:', error);
    }
  };

  const handleSearchUser = async () => {
    if (!searchEmail.trim()) {
      setError('请输入邮箱地址');
      return;
    }

    try {
      setSearchLoading(true);
      setError('');
      const user = await apiService.searchLarkUserByEmail(searchEmail.trim());
      setSearchedUser(user);
      setSuccess(`找到用户：${user.name} (${user.user_id})`);
      
      // 同时搜索该用户的绑定关系
      await loadBindings(user.user_id);
    } catch (error: any) {
      console.error('搜索用户失败:', error);
      setError(error.response?.data?.detail || '搜索用户失败');
      setSearchedUser(null);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleClearSearch = () => {
    setSearchEmail('');
    setSearchedUser(null);
    loadBindings();
  };

  const handleAddBinding = async () => {
    if (!newBinding.larkUserId.trim() || !newBinding.serverId) {
      setError('请填写完整信息');
      return;
    }

    try {
      setAddLoading(true);
      setError('');
      
      await apiService.createBinding(
        newBinding.larkUserId.trim(), 
        parseInt(newBinding.serverId)
      );
      
      setSuccess('绑定关系添加成功');
      setShowAddModal(false);
      setNewBinding({ larkUserId: '', serverId: '' });
      
      // 重新加载绑定关系
      await loadBindings(searchedUser?.user_id || undefined);
    } catch (error: any) {
      console.error('添加绑定关系失败:', error);
      setError(error.response?.data?.detail || '添加绑定关系失败');
    } finally {
      setAddLoading(false);
    }
  };

  const handleDeleteBinding = async (bindingId: number) => {
    if (!window.confirm('确定要删除这个绑定关系吗？')) {
      return;
    }

    try {
      setError('');
      await apiService.deleteBinding(bindingId);
      setSuccess('绑定关系删除成功');
      
      // 重新加载绑定关系
      await loadBindings(searchedUser?.user_id || undefined);
    } catch (error: any) {
      console.error('删除绑定关系失败:', error);
      setError('删除绑定关系失败');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h2>
              <i className="bi bi-link-45deg me-2"></i>
              绑定关系管理
            </h2>
            <Button
              variant="primary"
              onClick={() => {
                if (searchedUser) {
                  setNewBinding({ larkUserId: searchedUser.user_id, serverId: '' });
                  setShowAddModal(true);
                } else {
                  setError('请先搜索用户后再添加绑定关系');
                }
              }}
              className="d-flex align-items-center"
              disabled={!searchedUser}
            >
              <i className="bi bi-plus-circle me-2"></i>
              添加绑定
            </Button>
          </div>
        </Col>
      </Row>

      {/* 搜索栏 */}
      <Row className="mb-3">
        <Col md={8}>
          <InputGroup>
            <InputGroup.Text>
              <i className="bi bi-envelope"></i>
            </InputGroup.Text>
            <Form.Control
              type="email"
              placeholder="输入邮箱地址搜索Lark用户"
              value={searchEmail}
              onChange={(e) => setSearchEmail(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearchUser()}
            />
            <Button 
              variant="outline-secondary" 
              onClick={handleSearchUser}
              disabled={searchLoading}
            >
              {searchLoading ? (
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  className="me-1"
                />
              ) : (
                <i className="bi bi-search me-1"></i>
              )}
              搜索
            </Button>
            {searchedUser && (
              <Button variant="outline-danger" onClick={handleClearSearch}>
                <i className="bi bi-x me-1"></i>
                清除
              </Button>
            )}
          </InputGroup>
          {searchedUser && (
            <div className="mt-2 p-2 bg-light rounded">
              <small className="text-muted">已找到用户：</small>
              <strong className="ms-2">{searchedUser.name}</strong>
              <code className="ms-2 text-primary">{searchedUser.user_id}</code>
            </div>
          )}
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          <i className="bi bi-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          <i className="bi bi-check-circle me-2"></i>
          {success}
        </Alert>
      )}

      <Card className="shadow-sm">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">加载中...</span>
              </Spinner>
              <p className="mt-2 text-muted">加载绑定关系中...</p>
            </div>
          ) : bindings.length === 0 ? (
            <div className="text-center py-5">
              <i className="bi bi-link-45deg display-1 text-muted"></i>
              <p className="mt-3 text-muted">暂无绑定关系</p>
              <Button 
                variant="primary" 
                onClick={() => {
                  if (searchedUser) {
                    setNewBinding({ larkUserId: searchedUser.user_id, serverId: '' });
                    setShowAddModal(true);
                  } else {
                    setError('请先搜索用户后再添加绑定关系');
                  }
                }}
                disabled={!searchedUser}
              >
                <i className="bi bi-plus-circle me-2"></i>
                添加绑定关系
              </Button>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table striped hover>
                  <thead className="table-dark">
                    <tr>
                      <th>ID</th>
                      <th>Lark用户ID</th>
                      <th>服务器IP</th>
                      <th>服务器标签</th>
                      <th>创建时间</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bindings.map((binding) => (
                      <tr key={binding.id}>
                        <td>{binding.id}</td>
                        <td>
                          <code className="text-primary">{binding.lark_user_id}</code>
                        </td>
                        <td>{binding.server?.ip_address || '-'}</td>
                        <td>{binding.server?.label || '-'}</td>
                        <td className="text-muted">
                          {formatDate(binding.created_at)}
                        </td>
                        <td>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleDeleteBinding(binding.id)}
                          >
                            <i className="bi bi-trash me-1"></i>
                            删除
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
              
              <div className="mt-3 text-muted">
                <small>
                  <i className="bi bi-info-circle me-1"></i>
                  共 {bindings.length} 个绑定关系
                </small>
              </div>
            </>
          )}
        </Card.Body>
      </Card>

      {/* 添加绑定关系模态框 */}
      <Modal show={showAddModal} onHide={() => {
        setShowAddModal(false);
        setServerFilter('');
        setNewBinding({ larkUserId: '', serverId: '' });
      }}>
        <Modal.Header closeButton>
          <Modal.Title>添加绑定关系</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Lark用户ID</Form.Label>
              <Form.Control
                type="text"
                value={searchedUser?.user_id || newBinding.larkUserId}
                readOnly
                className="bg-light"
              />
              <Form.Text className="text-muted">
                {searchedUser ? `用户名：${searchedUser.name}` : '请先搜索用户'}
              </Form.Text>
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>服务器</Form.Label>
              <Form.Control
                type="text"
                placeholder="输入IP地址进行过滤..."
                value={serverFilter}
                onChange={(e) => setServerFilter(e.target.value)}
                className="mb-2"
              />
              <Form.Select
                value={newBinding.serverId}
                onChange={(e) => setNewBinding({
                  ...newBinding,
                  serverId: e.target.value
                })}
              >
                <option value="">请选择服务器</option>
                {servers
                  .filter(server => 
                    !serverFilter || 
                    (server.ip_address && server.ip_address.includes(serverFilter)) ||
                    (server.label && server.label.toLowerCase().includes(serverFilter.toLowerCase()))
                  )
                  .map((server) => (
                    <option key={server.id} value={server.id}>
                      {server.ip_address || server.vultr_id} - {server.label || 'No Label'}
                    </option>
                  ))
                }
              </Form.Select>
              {serverFilter && (
                <Form.Text className="text-muted">
                  显示 {servers.filter(server => 
                    (server.ip_address && server.ip_address.includes(serverFilter)) ||
                    (server.label && server.label.toLowerCase().includes(serverFilter.toLowerCase()))
                  ).length} / {servers.length} 个服务器
                </Form.Text>
              )}
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => {
            setShowAddModal(false);
            setServerFilter('');
            setNewBinding({ larkUserId: '', serverId: '' });
          }}>
            取消
          </Button>
          <Button 
            variant="primary" 
            onClick={handleAddBinding}
            disabled={addLoading || !searchedUser || !newBinding.serverId}
          >
            {addLoading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  className="me-2"
                />
                添加中...
              </>
            ) : (
              '添加'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default Bindings;
