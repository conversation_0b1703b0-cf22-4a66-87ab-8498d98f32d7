/* 全局样式 */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义样式 */
.min-vh-100 {
  min-height: 100vh;
}

/* 卡片阴影效果 */
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* 表格样式优化 */
.table {
  margin-bottom: 0;
}

.table thead th {
  border-top: none;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

/* 导航栏样式 */
.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.navbar-nav .nav-link:hover {
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
}

/* 按钮样式优化 */
.btn {
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

/* 徽章样式 */
.badge {
  font-size: 0.75em;
  font-weight: 500;
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: 0.75rem;
  transition: all 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* 表单样式 */
.form-control {
  border-radius: 0.5rem;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select {
  border-radius: 0.5rem;
}

/* 模态框样式 */
.modal-content {
  border: none;
  border-radius: 0.75rem;
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.75rem 0.75rem 0 0;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 0.75rem 0.75rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/* 加载动画 */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* 空状态样式 */
.display-1 {
  font-size: 4rem;
  opacity: 0.3;
}

/* 代码块样式 */
code {
  background-color: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

/* 渐变背景 */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 状态颜色 */
.text-success {
  color: #198754 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #fd7e14 !important;
}

.text-info {
  color: #0dcaf0 !important;
}

.text-primary {
  color: #0d6efd !important;
}