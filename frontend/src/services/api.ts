// API服务类
import axios, { AxiosInstance, AxiosResponse } from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// 接口类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface User {
  id: number;
  username: string;
  email?: string;
}

export interface Server {
  id: number;
  vultr_id: string;
  ip_address?: string;
  label?: string;
  status?: string;
  region?: string;
  created_at: string;
  updated_at: string;
}

export interface ServerListResponse {
  total: number;
  items: Server[];
}

export interface UserServerBinding {
  id: number;
  lark_user_id: string;
  server_id: number;
  server: Server;
  created_at: string;
}

export interface OperationLog {
  id: number;
  lark_user_id: string;
  server_id?: number;
  operation_type: 'restart' | 'sync';
  status: 'success' | 'failed' | 'in_progress';
  result?: string;
  error_message?: string;
  created_at: string;
  server?: Server;
}

export interface OperationLogListResponse {
  total: number;
  items: OperationLog[];
}

export interface LarkUser {
  user_id: string;
  name: string;
  email: string;
  avatar?: string;
}

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
    });

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // 响应拦截器 - 处理认证错误
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // 认证相关
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    const response: AxiosResponse<LoginResponse> = await this.api.post('/auth/login', formData);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get('/auth/me');
    return response.data;
  }

  // 服务器管理
  async getServers(skip: number = 0, limit: number = 100): Promise<ServerListResponse> {
    const response: AxiosResponse<ServerListResponse> = await this.api.get('/servers', {
      params: { skip, limit }
    });
    return response.data;
  }

  async syncServers(): Promise<{ message: string; synced_count: number }> {
    const response = await this.api.post('/servers/sync');
    return response.data;
  }

  // 绑定关系管理
  async getBindings(larkUserId?: string): Promise<UserServerBinding[]> {
    const params = larkUserId ? { lark_user_id: larkUserId } : {};
    const response: AxiosResponse<UserServerBinding[]> = await this.api.get('/bindings', {
      params
    });
    return response.data;
  }

  async createBinding(larkUserId: string, serverId: number): Promise<UserServerBinding> {
    const response: AxiosResponse<UserServerBinding> = await this.api.post('/bindings', {
      lark_user_id: larkUserId,
      server_id: serverId
    });
    return response.data;
  }

  async deleteBinding(bindingId: number): Promise<void> {
    await this.api.delete(`/bindings/${bindingId}`);
  }

  // 操作日志
  async getOperationLogs(skip: number = 0, limit: number = 100): Promise<OperationLogListResponse> {
    const response: AxiosResponse<OperationLogListResponse> = await this.api.get('/logs', {
      params: { skip, limit }
    });
    return response.data;
  }

  // Lark用户搜索
  async searchLarkUserByEmail(email: string): Promise<LarkUser> {
    const response: AxiosResponse<LarkUser> = await this.api.get('/lark/search-user', {
      params: { email }
    });
    return response.data;
  }

  // Token管理
  setToken(token: string): void {
    localStorage.setItem('access_token', token);
  }

  getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  clearToken(): void {
    localStorage.removeItem('access_token');
  }
}

export const apiService = new ApiService();
export default apiService;
