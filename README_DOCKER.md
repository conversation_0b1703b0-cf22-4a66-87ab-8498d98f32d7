# Lark机器人系统 - Docker部署指南

## 🚀 快速开始

### 1. 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 8GB+ 内存
- 20GB+ 磁盘空间

### 2. 克隆项目

```bash
git clone <repository-url>
cd lark_robot
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp env.example .env

# 编辑环境变量文件
vim .env
```

**必需配置的环境变量：**

```bash
# Lark应用配置
LARK_APP_ID=your_lark_app_id
LARK_APP_SECRET=your_lark_app_secret
LARK_ENCRYPT_KEY=your_lark_encrypt_key
LARK_VERIFY_TOKEN=your_lark_verify_token

# Vultr API配置
VULTR_API_KEY=your_vultr_api_key

# 安全密钥（生产环境必须更改）
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars

# MySQL数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_user_password
```

### 4. 一键部署

```bash
# 使用部署脚本
./deploy.sh
```

或手动部署：

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🏗️ 架构组件

### 服务组件

| 服务名 | 端口 | 描述 |
|--------|------|------|
| **frontend** | 80 | React前端界面 |
| **backend** | 8000 | FastAPI后端API |
| **mysql** | 3306 | MySQL数据库 |
| **redis** | 6379 | Redis缓存 |
| **celery_worker** | - | Celery异步任务处理 |
| **celery_beat** | - | Celery定时任务调度 |

### 数据卷

| 卷名 | 挂载点 | 用途 |
|------|--------|------|
| `mysql_data` | `/var/lib/mysql` | MySQL数据持久化 |
| `redis_data` | `/data` | Redis数据持久化 |
| `backend_data` | `/app/data` | 后端应用数据 |

## 🔧 管理命令

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart [service_name]

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]

# 进入容器
docker-compose exec [service_name] bash
```

### 数据库管理

```bash
# 使用数据库管理脚本
./scripts/db_management.sh help

# 常用数据库操作
./scripts/db_management.sh status    # 查看数据库状态
./scripts/db_management.sh connect   # 连接数据库
./scripts/db_management.sh backup    # 备份数据库
./scripts/db_management.sh reset     # 重置数据库
```

### 应用管理

```bash
# 查看后端API文档
open http://localhost:8000/docs

# 查看前端界面
open http://localhost

# 健康检查
curl http://localhost:8000/health
curl http://localhost/health
```

## 🛠️ 开发环境

### 启动开发环境

```bash
# 使用开发配置启动
docker-compose -f docker-compose.dev.yml up -d

# 前端热重载开发
cd frontend && npm start
```

### 开发环境特点

- 代码热重载
- 详细调试日志
- 开发数据库（独立）
- 前端开发服务器

## 📊 监控和日志

### 查看日志

```bash
# 所有服务日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f backend
docker-compose logs -f mysql
docker-compose logs -f celery_worker

# 实时日志
docker-compose logs -f --tail=100
```

### 健康检查

```bash
# 检查所有服务健康状态
docker-compose ps

# 手动健康检查
curl http://localhost:8000/health
curl http://localhost/health
```

## 🔒 安全配置

### 生产环境安全建议

1. **更改默认密码**
   ```bash
   # 在.env文件中设置强密码
   MYSQL_ROOT_PASSWORD=your_very_secure_password
   SECRET_KEY=your-very-long-random-secret-key
   ```

2. **网络安全**
   - 使用防火墙限制端口访问
   - 配置SSL/TLS证书
   - 设置反向代理

3. **数据备份**
   ```bash
   # 定期备份数据库
   ./scripts/db_management.sh backup
   
   # 备份Docker卷
   docker run --rm -v mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_backup.tar.gz -C /data .
   ```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   docker-compose logs [service_name]
   
   # 重新构建镜像
   docker-compose build --no-cache [service_name]
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   docker-compose exec mysql mysqladmin ping -h localhost -u root -p
   
   # 重置数据库
   ./scripts/db_management.sh reset
   ```

3. **前端访问失败**
   ```bash
   # 检查nginx配置
   docker-compose exec frontend nginx -t
   
   # 重启前端服务
   docker-compose restart frontend
   ```

4. **Celery任务失败**
   ```bash
   # 检查Celery Worker状态
   docker-compose exec celery_worker celery -A tasks.celery_app inspect ping
   
   # 查看任务队列
   docker-compose exec celery_worker celery -A tasks.celery_app inspect active
   ```

### 性能优化

1. **内存优化**
   - 调整MySQL缓存大小
   - 设置Celery并发数
   - 配置Redis内存策略

2. **存储优化**
   - 定期清理日志文件
   - 压缩数据库备份
   - 使用SSD存储

## 📝 版本更新

### 更新应用

```bash
# 拉取最新代码
git pull

# 停止服务
docker-compose down

# 重新构建和启动
docker-compose build --no-cache
docker-compose up -d

# 检查服务状态
docker-compose ps
```

### 数据迁移

```bash
# 备份当前数据
./scripts/db_management.sh backup

# 更新后恢复数据（如需要）
./scripts/db_management.sh restore backup_file.sql
```

## 📞 技术支持

- **项目文档**: 查看 `docs/` 目录
- **API文档**: http://localhost:8000/docs
- **问题反馈**: 提交 GitHub Issue

---

**默认登录信息:**
- 用户名: `admin`
- 密码: `admin123`

**服务访问地址:**
- 前端: http://localhost
- API文档: http://localhost:8000/docs
- MySQL: localhost:3306
- Redis: localhost:6379


