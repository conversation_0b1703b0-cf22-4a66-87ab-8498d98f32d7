"""绑定关系模型"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class UserServerBinding(Base):
    """Lark用户与服务器绑定关系表"""
    __tablename__ = "user_server_bindings"
    
    id = Column(Integer, primary_key=True, index=True)
    lark_user_id = Column(String(100), index=True, nullable=False, comment="Lark用户ID")
    server_id = Column(Integer, ForeignKey("servers.id"), nullable=False, comment="服务器ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关联关系
    server = relationship("Server", backref="bindings")
    
    def __repr__(self):
        return f"<UserServerBinding(id={self.id}, lark_user_id='{self.lark_user_id}', server_id={self.server_id})>"
