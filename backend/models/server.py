"""服务器模型"""
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.sql import func
from database import Base

class Server(Base):
    """Vultr裸机服务器表"""
    __tablename__ = "servers"
    
    id = Column(Integer, primary_key=True, index=True)
    vultr_id = Column(String(50), unique=True, index=True, nullable=False, comment="Vultr服务器ID")
    ip_address = Column(String(15), index=True, comment="服务器IP地址")
    label = Column(String(100), comment="服务器标签")
    status = Column(String(20), comment="服务器状态")
    region = Column(String(50), comment="服务器区域")
    os = Column(String(100), comment="操作系统")
    plan = Column(String(50), comment="服务器套餐")
    raw_data = Column(Text, comment="原始数据JSON")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<Server(id={self.id}, vultr_id='{self.vultr_id}', ip='{self.ip_address}')>"
