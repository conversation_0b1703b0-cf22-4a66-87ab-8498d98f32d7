"""应用配置"""
import os
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    APP_NAME: str = "Lark机器人服务器重启系统"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = "mysql+pymysql://user:password@localhost:3306/lark_robot"
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    
    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Vultr API配置
    VULTR_API_KEY: str = "your-vultr-api-key"
    VULTR_API_BASE_URL: str = "https://api.vultr.com"
    
    # Lark配置
    LARK_APP_ID: str = "your-lark-app-id"
    LARK_APP_SECRET: str = "your-lark-app-secret"
    LARK_ENCRYPT_KEY: str = "your-lark-encrypt-key"
    LARK_VERIFY_TOKEN: str = "your-lark-verify-token"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "logs"
    
    # 定时任务配置
    SERVER_SYNC_INTERVAL_HOURS: int = 1
    RESTART_MONITOR_INTERVAL_SECONDS: int = 10
    RESTART_MAX_RETRIES: int = 6
    
    # 并发配置
    MAX_WORKERS: int = 10
    API_RATE_LIMIT: int = 100
    
    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'

# 创建全局设置实例
settings = Settings()
