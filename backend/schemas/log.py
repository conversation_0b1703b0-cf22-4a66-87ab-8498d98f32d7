"""日志相关的Pydantic模型"""
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from models.log import OperationType, OperationStatus

class OperationLogCreate(BaseModel):
    """创建操作日志"""
    lark_user_id: str
    server_id: Optional[int] = None
    operation_type: OperationType
    status: OperationStatus
    result: Optional[str] = None
    error_message: Optional[str] = None

class OperationLogResponse(BaseModel):
    """操作日志响应"""
    id: int
    lark_user_id: str
    server_id: Optional[int] = None
    operation_type: OperationType
    status: OperationStatus
    result: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    server: Optional[dict] = None
    
    class Config:
        from_attributes = True

class OperationLogListResponse(BaseModel):
    """操作日志列表响应"""
    total: int
    items: List[OperationLogResponse]
