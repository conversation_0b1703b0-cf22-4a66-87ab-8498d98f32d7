"""认证相关的Pydantic模型"""
from pydantic import BaseModel, EmailStr
from typing import Optional

class UserLogin(BaseModel):
    """用户登录请求"""
    username: str
    password: str

class UserCreate(BaseModel):
    """创建用户请求"""
    username: str
    password: str
    email: Optional[EmailStr] = None

class UserResponse(BaseModel):
    """用户响应"""
    id: int
    username: str
    email: Optional[str] = None
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    """JWT令牌响应"""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """JWT令牌数据"""
    username: Optional[str] = None
