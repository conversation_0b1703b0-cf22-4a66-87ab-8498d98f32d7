"""服务器相关的Pydantic模型"""
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class ServerBase(BaseModel):
    """服务器基础信息"""
    vultr_id: str
    ip_address: Optional[str] = None
    label: Optional[str] = None
    status: Optional[str] = None
    region: Optional[str] = None
    os: Optional[str] = None
    plan: Optional[str] = None

class ServerCreate(ServerBase):
    """创建服务器"""
    raw_data: Optional[str] = None

class ServerUpdate(BaseModel):
    """更新服务器"""
    ip_address: Optional[str] = None
    label: Optional[str] = None
    status: Optional[str] = None
    region: Optional[str] = None
    os: Optional[str] = None
    plan: Optional[str] = None
    raw_data: Optional[str] = None

class ServerResponse(ServerBase):
    """服务器响应"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ServerListResponse(BaseModel):
    """服务器列表响应"""
    total: int
    items: List[ServerResponse]

class UserServerBindingCreate(BaseModel):
    """创建用户服务器绑定"""
    lark_user_id: str
    server_id: int

class UserServerBindingResponse(BaseModel):
    """用户服务器绑定响应"""
    id: int
    lark_user_id: str
    server_id: int
    server: ServerResponse
    created_at: datetime
    
    class Config:
        from_attributes = True

class RestartRequest(BaseModel):
    """重启请求"""
    server_input: str  # IP或ID
