"""Celery应用配置"""
from celery import Celery
from config import settings

# 创建Celery应用
celery_app = Celery(
    "lark_robot",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=[
        "tasks.server_sync",
        "tasks.restart_monitor"
    ]
)

# 配置Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# 定时任务配置
celery_app.conf.beat_schedule = {
    'sync-servers-hourly': {
        'task': 'tasks.server_sync.sync_servers_task',
        'schedule': settings.SERVER_SYNC_INTERVAL_HOURS * 3600.0,  # 转换为秒
    },
}

if __name__ == "__main__":
    celery_app.start()
