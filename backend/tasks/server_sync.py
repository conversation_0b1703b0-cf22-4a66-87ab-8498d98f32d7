"""服务器同步任务"""
from celery import current_task
from sqlalchemy.orm import sessionmaker
from tasks.celery_app import celery_app
from database import engine
from services.server_service import ServerService
from models.log import OperationType, OperationStatus
from utils.logger import app_logger

# 创建数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@celery_app.task(bind=True)
def sync_servers_task(self):
    """同步服务器信息任务"""
    import asyncio
    db = SessionLocal()
    try:
        app_logger.info("开始执行服务器同步任务")
        
        # 在新的事件循环中执行异步同步
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        synced_count = loop.run_until_complete(ServerService.sync_servers_from_vultr(db))
        loop.close()
        
        # 记录同步日志
        ServerService.create_operation_log(
            db, "system", None, OperationType.SYNC, OperationStatus.SUCCESS,
            result=f"成功同步 {synced_count} 台服务器"
        )
        
        app_logger.info(f"服务器同步任务完成，同步了 {synced_count} 台服务器")
        return {"synced_count": synced_count, "status": "success"}
        
    except Exception as e:
        app_logger.error(f"服务器同步任务失败: {e}")
        
        # 记录失败日志
        try:
            ServerService.create_operation_log(
                db, "system", None, OperationType.SYNC, OperationStatus.FAILED,
                error_message=str(e)
            )
        except:
            pass
        
        # 重试任务
        raise self.retry(exc=e, countdown=60, max_retries=3)
        
    finally:
        db.close()

@celery_app.task
def manual_sync_servers_task():
    """手动同步服务器信息任务"""
    import asyncio
    db = SessionLocal()
    try:
        app_logger.info("开始执行手动服务器同步任务")
        
        # 在新的事件循环中执行异步同步
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        synced_count = loop.run_until_complete(ServerService.sync_servers_from_vultr(db))
        loop.close()
        
        app_logger.info(f"手动服务器同步任务完成，同步了 {synced_count} 台服务器")
        return {"synced_count": synced_count, "status": "success"}
        
    except Exception as e:
        app_logger.error(f"手动服务器同步任务失败: {e}")
        raise e
        
    finally:
        db.close()
