"""FastAPI应用主入口"""
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager

from config import settings
from database import engine, Base
from api import auth, server, binding, log, lark
from utils.logger import app_logger

# 创建数据库表
Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    app_logger.info("应用启动")
    yield
    app_logger.info("应用关闭")

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    description="Lark机器人服务器重启系统API",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    app_logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"}
    )

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(server.router, prefix="/api/servers", tags=["服务器"])
app.include_router(binding.router, prefix="/api/bindings", tags=["绑定关系"])
app.include_router(log.router, prefix="/api/logs", tags=["操作日志"])
# Lark路由需要分别注册webhook和API
app.include_router(lark.router, prefix="/webhook", tags=["Lark Webhook"])
app.include_router(lark.router, prefix="/api/lark", tags=["Lark API"])

@app.get("/")
async def root():
    """根路径"""
    return {"message": "Lark机器人服务器重启系统API"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "version": "1.0.0"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
