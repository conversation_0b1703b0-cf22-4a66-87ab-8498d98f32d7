"""通用消息处理服务"""
import json
import asyncio
from typing import Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from contextvars import ContextVar

from services.lark_service import lark_service
from services.server_service import ServerService
from services.vultr_service import vultr_service
from models.log import OperationType, OperationStatus
from utils.validators import parse_server_input
from utils.logger import app_logger
from tasks.restart_monitor import monitor_restart_task


class MessageService:
    """通用消息处理服务类"""
    
    # 使用ContextVar来管理请求上下文中的数据库会话和后台任务
    _current_db: ContextVar[Optional[Session]] = ContextVar('current_db', default=None)
    _current_background_tasks: ContextVar[Optional[BackgroundTasks]] = ContextVar('current_background_tasks', default=None)
    
    # 消息模板
    MESSAGES = {
        "invalid_input": "没看懂呢，请发送正确的远程电脑IP或机器ID，我能够帮助您重启远程电脑哦",
        "restarting": "您的远程电脑正在重启中，稍微休息一下，等待重启完成吧",
        "restart_success": "您的远程电脑重启完成啦",
        "restart_failed": "您的远程电脑重启失败，请联系管理员",
        "no_permission": "您的远程电脑重启失败，请发送自己的远程电脑IP或机器ID"
    }
    
    @staticmethod
    def parse_text_content(content: str) -> Optional[str]:
        """解析文本消息内容
        
        Args:
            content: 消息内容JSON字符串
            
        Returns:
            解析后的文本内容，失败返回None
        """
        try:
            content_data = json.loads(content)
            text = content_data.get("text", "").strip()
            return text if text else None
            
        except (json.JSONDecodeError, AttributeError) as e:
            app_logger.warning(f"解析消息内容失败: {e}")
            return None
    
    @staticmethod
    async def send_invalid_input_message(lark_user_id: str, message_type: str = "p2p"):
        """发送无效输入消息
        
        Args:
            lark_user_id: 用户ID
            message_type: 消息类型 (p2p/group)
        """
        message = MessageService.MESSAGES["invalid_input"]
        if message_type == "group":
            # 在群组中可以添加一些额外的提示
            message += "\n\n💡 提示：在群组中@我时，请直接发送服务器信息"
        
        await lark_service.send_text_message(lark_user_id, message)
    
    @staticmethod
    async def validate_and_parse_input(text: str, lark_user_id: str, db: Session, message_type: str = "p2p") -> Optional[object]:
        """验证和解析用户输入，返回服务器对象
        
        Args:
            text: 用户输入的文本
            lark_user_id: 用户ID
            db: 数据库会话
            message_type: 消息类型
            
        Returns:
            服务器对象，失败返回None
        """
        try:
            # 解析服务器输入
            input_type, value = parse_server_input(text)
            
            if input_type == "unknown":
                # 记录无效输入日志
                error_msg = f"{message_type}消息：无效的服务器IP或ID输入"
                ServerService.create_operation_log(
                    db, lark_user_id, None, OperationType.RESTART, OperationStatus.FAILED,
                    error_message=error_msg
                )
                app_logger.warning(f"{message_type}消息无效输入 - 用户: {lark_user_id}, 输入: {text}")
                await MessageService.send_invalid_input_message(lark_user_id, message_type)
                return None
            
            app_logger.info(f"解析服务器输入成功 - 类型: {input_type}, 值: {value}")
            
            # 查找服务器
            server = ServerService.get_server_by_input(db, text)
            if not server:
                # 记录服务器未找到日志
                error_msg = f"{message_type}消息：未找到服务器: {text}"
                ServerService.create_operation_log(
                    db, lark_user_id, None, OperationType.RESTART, OperationStatus.FAILED,
                    error_message=error_msg
                )
                app_logger.warning(f"{message_type}消息未找到服务器 - 用户: {lark_user_id}, 输入: {text}")
                await MessageService.send_invalid_input_message(lark_user_id, message_type)
                return None
            
            app_logger.info(f"找到服务器 - ID: {server.id}, 标签: {server.label}, IP: {server.ip_address}")
            return server
            
        except Exception as e:
            app_logger.error(f"{message_type}消息验证输入异常: {e}", exc_info=True)
            await MessageService.send_invalid_input_message(lark_user_id, message_type)
            return None
    
    @staticmethod
    async def check_and_create_permission(lark_user_id: str, server, db: Session, message_type: str = "p2p") -> bool:
        """检查用户权限，如果没有权限则尝试创建绑定关系
        
        Args:
            lark_user_id: 用户ID
            server: 服务器对象
            db: 数据库会话
            message_type: 消息类型
            
        Returns:
            是否有权限
        """
        try:
            # 检查权限
            has_permission = ServerService.check_user_server_permission(db, lark_user_id, server.id)
            
            if not has_permission:
                # 首次使用，自动创建绑定关系
                try:
                    ServerService.create_user_server_binding(db, lark_user_id, server.id)
                    app_logger.info(f"{message_type}消息自动创建用户服务器绑定关系: {lark_user_id} -> {server.id}")
                    return True
                except Exception as e:
                    app_logger.error(f"{message_type}消息创建绑定关系失败: {e}")
                    # 记录权限错误日志
                    error_msg = f"{message_type}消息：没有权限操作该服务器"
                    ServerService.create_operation_log(
                        db, lark_user_id, server.id, OperationType.RESTART, OperationStatus.FAILED,
                        error_message=error_msg
                    )
                    await lark_service.send_text_message(lark_user_id, MessageService.MESSAGES["no_permission"])
                    return False
            
            return True
            
        except Exception as e:
            app_logger.error(f"{message_type}消息权限检查异常: {e}", exc_info=True)
            await lark_service.send_text_message(lark_user_id, MessageService.MESSAGES["no_permission"])
            return False
    
    @staticmethod
    def set_context(db: Session, background_tasks: BackgroundTasks):
        """设置当前请求上下文的数据库会话和后台任务
        
        Args:
            db: 数据库会话
            background_tasks: 后台任务
        """
        MessageService._current_db.set(db)
        MessageService._current_background_tasks.set(background_tasks)
    
    @staticmethod
    def get_context() -> Tuple[Optional[Session], Optional[BackgroundTasks]]:
        """获取当前请求上下文的数据库会话和后台任务
        
        Returns:
            (数据库会话, 后台任务) 的元组
        """
        return MessageService._current_db.get(), MessageService._current_background_tasks.get()
    
    @staticmethod
    async def validate_and_process_message(
        lark_user_id: str, 
        msg_type: str, 
        content: str, 
        message_type: str,
        db: Session = None,
        background_tasks: BackgroundTasks = None
    ):
        """
        通用的消息验证和处理方法
        
        Args:
            lark_user_id: 用户ID
            msg_type: 消息类型
            content: 消息内容
            message_type: 消息类型标识 ("p2p" 或 "group")
            db: 数据库会话 (群组消息需要)
            background_tasks: 后台任务 (群组消息需要)
        """
        try:
            # 使用通用的消息验证逻辑
            is_valid, text = await MessageService.validate_message_input(lark_user_id, msg_type, content, message_type)
            if is_valid:
                # 创建异步任务处理消息
                asyncio.create_task(
                    MessageService.process_restart_request(lark_user_id, text, db, background_tasks, message_type)
                )
        except Exception as e:
            app_logger.error(f"通用消息验证和处理异常: {e}", exc_info=True)
            # 尝试发送错误消息给用户
            try:
                asyncio.create_task(MessageService.send_invalid_input_message(lark_user_id, message_type))
            except Exception as send_error:
                app_logger.error(f"发送错误消息失败: {send_error}")
    
    @staticmethod
    async def validate_message_input(lark_user_id: str, msg_type: str, content: dict, message_type: str = "p2p") -> Tuple[bool, str]:
        """验证消息输入的通用逻辑
        
        Args:
            lark_user_id: Lark用户ID
            msg_type: 消息类型
            content: 消息内容
            message_type: 消息来源类型（p2p或group）
            
        Returns:
            Tuple[bool, str]: (是否验证通过, 解析后的文本内容)
        """
        # 检查用户ID
        if not lark_user_id:
            app_logger.warning(f"{message_type.upper()}消息无法获取Lark用户ID")
            return False, ""
        
        # 只处理文本消息
        if msg_type != "text":
            app_logger.info(f"{message_type.upper()}消息非文本类型: {msg_type}，忽略处理")
            asyncio.create_task(MessageService.send_invalid_input_message(lark_user_id, message_type))
            return False, ""
        
        # 解析文本内容
        text = MessageService.parse_text_content(json.dumps(content) if isinstance(content, dict) else content)
        if not text:
            app_logger.warning(f"{message_type.upper()}消息文本内容为空或解析失败")
            asyncio.create_task(MessageService.send_invalid_input_message(lark_user_id, message_type))
            return False, ""
            
        return True, text
    
    # process_message_async 方法已移除
    # 现在统一使用 process_restart_request 处理所有消息
    
    @staticmethod
    async def process_restart_request(lark_user_id: str, text: str, db: Session = None, background_tasks: BackgroundTasks = None, message_type: str = "p2p"):
        """处理重启请求的通用逻辑
        
        Args:
            lark_user_id: 用户ID
            text: 消息文本内容
            db: 数据库会话（可选，如果不提供则从上下文获取）
            background_tasks: 后台任务（可选，如果不提供则从上下文获取）
            message_type: 消息类型 (p2p/group)
        """
        try:
            # 如果没有提供db和background_tasks，从上下文获取
            if db is None or background_tasks is None:
                context_db, context_bg_tasks = MessageService.get_context()
                db = db or context_db
                background_tasks = background_tasks or context_bg_tasks
            
            if db is None or background_tasks is None:
                app_logger.error(f"{message_type}消息处理失败：缺少数据库会话或后台任务上下文")
                return
            
            app_logger.info(f"开始处理{message_type}消息重启请求 - 用户: {lark_user_id}, 内容: {text}")
            
            # 验证和解析输入
            server = await MessageService.validate_and_parse_input(text, lark_user_id, db, message_type)
            if not server:
                return
            
            # 检查权限
            has_permission = await MessageService.check_and_create_permission(lark_user_id, server, db, message_type)
            if not has_permission:
                return
            
            # 开始重启流程
            await MessageService.start_restart_process(lark_user_id, server, db, background_tasks)
            
            app_logger.info(f"{message_type}消息处理完成 - 用户: {lark_user_id}, 服务器: {server.id}")
            
        except Exception as e:
            app_logger.error(f"{message_type}消息处理异常 - 用户: {lark_user_id}, 错误: {e}", exc_info=True)
            # 发送错误消息
            await lark_service.send_text_message(lark_user_id, MessageService.MESSAGES["restart_failed"])
    
    @staticmethod
    async def start_restart_process(lark_user_id: str, server, db: Session, background_tasks: BackgroundTasks):
        """开始重启流程"""
        try:
            # 创建操作日志
            operation_log = ServerService.create_operation_log(
                db, lark_user_id, server.id, OperationType.RESTART, OperationStatus.IN_PROGRESS,
                result="开始重启服务器"
            )
            
            # 调用Vultr重启API
            restart_success = await vultr_service.reboot_bare_metal(server.vultr_id)
            
            if not restart_success:
                # 更新日志为失败状态
                operation_log.status = OperationStatus.FAILED
                operation_log.error_message = "调用Vultr重启API失败"
                db.commit()
                
                await lark_service.send_text_message(lark_user_id, MessageService.MESSAGES["restart_failed"])
                return
            
            # 发送重启中消息
            await lark_service.send_text_message(lark_user_id, MessageService.MESSAGES["restarting"])
            
            # 启动后台任务监控重启状态
            background_tasks.add_task(
                monitor_restart_task,
                operation_log.id,
                server.vultr_id,
                lark_user_id
            )
            
        except Exception as e:
            app_logger.error(f"重启流程异常: {e}")
            # 更新日志为失败状态
            try:
                operation_log.status = OperationStatus.FAILED
                operation_log.error_message = f"重启流程异常: {str(e)}"
                db.commit()
            except:
                pass
            
            await lark_service.send_text_message(lark_user_id, MessageService.MESSAGES["restart_failed"])