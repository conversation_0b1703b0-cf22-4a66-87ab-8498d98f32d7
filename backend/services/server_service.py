"""服务器服务"""
import json
from typing import List, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from models.server import Server
from models.binding import UserServerBinding
from models.log import OperationLog, OperationType, OperationStatus
from schemas.server import ServerCreate, ServerUpdate
from services.vultr_service import vultr_service
from utils.validators import parse_server_input
from utils.logger import app_logger

class ServerService:
    """服务器服务类"""
    
    @staticmethod
    async def sync_servers_from_vultr(db: Session) -> int:
        """从Vultr同步服务器信息"""
        try:
            vultr_servers = await vultr_service.list_bare_metals()
            synced_count = 0
            
            for vultr_server in vultr_servers:
                vultr_id = vultr_server.get("id")
                if not vultr_id:
                    continue
                
                # 检查服务器是否已存在
                existing_server = db.query(Server).filter(Server.vultr_id == vultr_id).first()
                
                server_data = {
                    "vultr_id": vultr_id,
                    "ip_address": vultr_server.get("main_ip"),
                    "label": vultr_server.get("label"),
                    "status": vultr_server.get("status"),
                    "region": vultr_server.get("region"),
                    "os": vultr_server.get("os"),
                    "plan": vultr_server.get("plan"),
                    "raw_data": json.dumps(vultr_server)
                }
                
                if existing_server:
                    # 更新现有服务器
                    for key, value in server_data.items():
                        if key != "vultr_id":  # 不更新vultr_id
                            setattr(existing_server, key, value)
                else:
                    # 创建新服务器
                    new_server = Server(**server_data)
                    db.add(new_server)
                
                synced_count += 1
            
            db.commit()
            app_logger.info(f"成功同步 {synced_count} 台服务器信息")
            return synced_count
            
        except Exception as e:
            app_logger.error(f"同步服务器信息失败: {e}")
            db.rollback()
            return 0
    
    @staticmethod
    def get_server_by_input(db: Session, server_input: str) -> Optional[Server]:
        """根据用户输入获取服务器"""
        input_type, value = parse_server_input(server_input)
        
        if input_type == "ip":
            return db.query(Server).filter(Server.ip_address == value).first()
        elif input_type == "id":
            return db.query(Server).filter(Server.vultr_id == value).first()
        else:
            return None
    
    @staticmethod
    def check_user_server_permission(db: Session, lark_user_id: str, server_id: int) -> bool:
        """检查用户是否有服务器权限"""
        binding = db.query(UserServerBinding).filter(
            UserServerBinding.lark_user_id == lark_user_id,
            UserServerBinding.server_id == server_id
        ).first()
        return binding is not None
    
    @staticmethod
    def create_user_server_binding(db: Session, lark_user_id: str, server_id: int) -> UserServerBinding:
        """创建用户服务器绑定关系"""
        # 检查是否已存在绑定关系
        existing_binding = db.query(UserServerBinding).filter(
            UserServerBinding.lark_user_id == lark_user_id,
            UserServerBinding.server_id == server_id
        ).first()
        
        if existing_binding:
            return existing_binding
        
        binding = UserServerBinding(
            lark_user_id=lark_user_id,
            server_id=server_id
        )
        db.add(binding)
        db.commit()
        db.refresh(binding)
        return binding
    
    @staticmethod
    def get_user_bindings(db: Session, lark_user_id: str) -> List[UserServerBinding]:
        """获取用户的服务器绑定关系"""
        return db.query(UserServerBinding).filter(
            UserServerBinding.lark_user_id == lark_user_id
        ).all()
    
    @staticmethod
    def delete_user_server_binding(db: Session, binding_id: int) -> bool:
        """删除用户服务器绑定关系"""
        binding = db.query(UserServerBinding).filter(UserServerBinding.id == binding_id).first()
        if binding:
            db.delete(binding)
            db.commit()
            return True
        return False
    
    @staticmethod
    def create_operation_log(db: Session, lark_user_id: str, server_id: Optional[int], 
                           operation_type: OperationType, status: OperationStatus,
                           result: Optional[str] = None, error_message: Optional[str] = None) -> OperationLog:
        """创建操作日志"""
        log = OperationLog(
            lark_user_id=lark_user_id,
            server_id=server_id,
            operation_type=operation_type,
            status=status,
            result=result,
            error_message=error_message
        )
        db.add(log)
        db.commit()
        db.refresh(log)
        return log
    
    @staticmethod
    def get_servers(db: Session, skip: int = 0, limit: int = 100) -> Tuple[List[Server], int]:
        """获取服务器列表"""
        total = db.query(Server).count()
        servers = db.query(Server).offset(skip).limit(limit).all()
        return servers, total
    
    @staticmethod
    def get_operation_logs(db: Session, skip: int = 0, limit: int = 100) -> Tuple[List[OperationLog], int]:
        """获取操作日志列表"""
        total = db.query(OperationLog).count()
        logs = db.query(OperationLog).order_by(OperationLog.created_at.desc()).offset(skip).limit(limit).all()
        return logs, total
