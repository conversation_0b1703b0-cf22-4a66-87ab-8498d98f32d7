"""群组消息处理服务"""
import json
import asyncio
import re
from typing import Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from lark_oapi.api.im.v1 import P2GroupAtMessageReceiveV1

from services.lark_service import lark_service
from services.message_service import MessageService
from utils.logger import app_logger


class GroupMessageService:
    """群组消息处理服务类"""
    
    @staticmethod
    def extract_message_info(req: P2GroupAtMessageReceiveV1) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """从群组@消息请求中提取消息信息
        
        Args:
            req: 群组@消息接收请求
            
        Returns:
            Tuple[用户ID, 消息类型, 消息内容]
        """
        try:
            event = req.event
            message = event.message
            sender = event.sender
            
            lark_user_id = sender.sender_id.user_id if sender and sender.sender_id else None
            msg_type = message.message_type if message else None
            content = message.content if message else None
            
            app_logger.info(f"收到群组@消息 - 用户ID: {lark_user_id}, 消息类型: {msg_type}, 内容: {content}")
            
            return lark_user_id, msg_type, content
            
        except Exception as e:
            app_logger.error(f"提取群组消息信息异常: {e}")
            return None, None, None
    
    @staticmethod
    def parse_text_content(content: str) -> Optional[str]:
        """解析群组文本消息内容，移除@标记
        
        Args:
            content: 消息内容JSON字符串
            
        Returns:
            解析后的文本内容，失败返回None
        """
        try:
            # 使用基础解析方法
            text = MessageService.parse_text_content(content)
            
            if text:
                # 移除@机器人的部分，提取实际命令
                # 群组消息中可能包含@用户的格式，需要清理
                # 移除@用户标记，保留实际的服务器信息
                text = re.sub(r'<at[^>]*>[^<]*</at>', '', text).strip()
                
            return text if text else None
            
        except Exception as e:
            app_logger.warning(f"解析群组消息内容失败: {e}")
            return None
