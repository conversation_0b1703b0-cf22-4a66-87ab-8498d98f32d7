"""Lark API服务"""
import lark_oapi as lark
from lark_oapi.api.contact.v3 import *
from lark_oapi.api.im.v1 import *
import json
from typing import Dict, Optional
from config import settings
from utils.logger import app_logger

class LarkService:
    """Lark API服务类"""
    
    def __init__(self):
        self.app_id = settings.LARK_APP_ID
        self.app_secret = settings.LARK_APP_SECRET
        self.encrypt_key = settings.LARK_ENCRYPT_KEY
        self.verify_token = settings.LARK_VERIFY_TOKEN
        
        # 创建Lark客户端
        self.client = lark.Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .log_level(lark.LogLevel.INFO) \
            .build()
    
    def get_client(self) -> lark.Client:
        """获取Lark客户端"""
        return self.client
    
    async def send_message(self, receive_id: str, msg_type: str, content: str, receive_id_type: str = "user_id") -> bool:
        """发送消息"""
        try:
            # 构建消息请求
            request = CreateMessageRequest.builder() \
                .receive_id_type(receive_id_type) \
                .request_body(CreateMessageRequestBody.builder()
                    .receive_id(receive_id)
                    .msg_type(msg_type)
                    .content(content)
                    .build()) \
                .build()
            
            # 发送消息
            response = self.client.im.v1.message.create(request)
            
            if not response.success():
                app_logger.error(f"发送Lark消息失败: {response.code}, {response.msg}")
                return False
            
            app_logger.info(f"成功发送Lark消息到用户: {receive_id}")
            return True
            
        except Exception as e:
            app_logger.error(f"发送Lark消息异常: {e}")
            return False
    
    async def send_text_message(self, user_id: str, text: str) -> bool:
        """发送文本消息"""
        content = json.dumps({"text": text})
        return await self.send_message(user_id, "text", content)
    
    # verify_webhook_signature方法已被移除
    # 现在使用EventDispatcherHandler内置的签名验证功能
    
    async def get_user_info(self, user_id: str) -> Optional[Dict]:
        """获取用户信息"""
        try:
            # 构建获取用户信息请求
            request = GetUserRequest.builder() \
                .user_id_type("user_id") \
                .user_id(user_id) \
                .build()
            
            # 获取用户信息
            response = self.client.contact.v3.user.get(request)
            
            if not response.success():
                app_logger.error(f"获取Lark用户信息失败: {response.code}, {response.msg}")
                return None
            
            return response.data.user.__dict__ if response.data.user else None
            
        except Exception as e:
            app_logger.error(f"获取Lark用户信息异常: {e}")
            return None
    
    async def search_user_by_email(self, email: str) -> Optional[Dict]:
        """根据邮箱搜索用户"""
        try:
            # 检查Lark配置是否有效
            if not self.app_id or not self.app_secret or self.app_id == "your-lark-app-id":
                app_logger.error("Lark配置无效，请检查APP_ID和APP_SECRET")
                return None
            
            from lark_oapi.api.contact.v3 import BatchGetIdUserRequest, BatchGetIdUserRequestBody
            
            # 构建批量获取用户ID请求
            request = BatchGetIdUserRequest.builder() \
                .user_id_type("user_id") \
                .request_body(BatchGetIdUserRequestBody.builder()
                    .emails([email])
                    .include_resigned(False)
                    .build()) \
                .build()
            
            # 获取用户ID
            response = self.client.contact.v3.user.batch_get_id(request)
            
            if not response.success():
                app_logger.error(f"根据邮箱搜索Lark用户失败: code={response.code}, msg={response.msg}")
                app_logger.error(f"请检查Lark应用配置和权限设置")
                return None
            
            # 如果找到用户，返回用户信息
            if response.data and response.data.user_list:
                user_info = response.data.user_list[0]
                if user_info and user_info.user_id:
                    # 返回基本用户信息
                    return {
                        'user_id': user_info.user_id,
                        'name': email.split('@')[0],  # 使用邮箱前缀作为临时名称
                        'email': email
                    }
            
            app_logger.warning(f"未找到邮箱为 {email} 的用户")
            return None
            
        except Exception as e:
            app_logger.error(f"根据邮箱搜索Lark用户异常: {e}")
            app_logger.error("请确认：1. Lark应用配置正确 2. 应用已获得'获取用户 user ID'权限 3. 用户在企业通讯录中")
            return None

# 创建全局实例
lark_service = LarkService()
