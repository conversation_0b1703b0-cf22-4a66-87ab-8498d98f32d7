#!/usr/bin/env python3
"""启动Celery worker和beat的脚本"""
import os
import sys
import subprocess
from multiprocessing import Process

def start_worker():
    """启动Celery worker"""
    os.system("celery -A backend.tasks.celery_app worker --loglevel=info")

def start_beat():
    """启动Celery beat"""
    os.system("celery -A backend.tasks.celery_app beat --loglevel=info")

def start_flower():
    """启动Celery flower监控"""
    os.system("celery -A backend.tasks.celery_app flower")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python start_celery.py [worker|beat|flower|all]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "worker":
        start_worker()
    elif command == "beat":
        start_beat()
    elif command == "flower":
        start_flower()
    elif command == "all":
        # 启动所有服务
        worker_process = Process(target=start_worker)
        beat_process = Process(target=start_beat)
        
        worker_process.start()
        beat_process.start()
        
        try:
            worker_process.join()
            beat_process.join()
        except KeyboardInterrupt:
            print("正在停止Celery服务...")
            worker_process.terminate()
            beat_process.terminate()
            worker_process.join()
            beat_process.join()
    else:
        print("Invalid command. Use: worker, beat, flower, or all")
        sys.exit(1)
