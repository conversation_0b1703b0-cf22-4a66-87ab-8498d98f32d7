"""验证器工具"""
import re
from typing import Optional

def is_valid_ip(ip: str) -> bool:
    """验证IP地址格式"""
    pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if not re.match(pattern, ip):
        return False
    
    parts = ip.split('.')
    for part in parts:
        if int(part) > 255:
            return False
    
    return True

def is_valid_vultr_id(vultr_id: str) -> bool:
    """验证Vultr ID格式"""
    # Vultr ID通常是UUID格式或特定格式的字符串
    pattern = r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$'
    return bool(re.match(pattern, vultr_id, re.IGNORECASE))

def parse_server_input(server_input: str) -> tuple[str, str]:
    """
    解析服务器输入，返回类型和值
    返回: (type, value) where type is 'ip' or 'id'
    """
    server_input = server_input.strip()
    
    if is_valid_ip(server_input):
        return 'ip', server_input
    elif is_valid_vultr_id(server_input):
        return 'id', server_input
    else:
        return 'unknown', server_input

def validate_lark_user_id(lark_user_id: str) -> bool:
    """验证Lark用户ID格式"""
    # Lark用户ID通常是特定格式的字符串
    return bool(lark_user_id and len(lark_user_id.strip()) > 0)
