"""日志配置"""
import logging
import os
from logging.handlers import TimedRotatingFileHandler
from config import settings

def setup_logger(name: str) -> logging.Logger:
    """设置日志器"""
    logger = logging.getLogger(name)
    
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # 创建日志目录
    os.makedirs(settings.LOG_DIR, exist_ok=True)
    
    # 文件处理器 - 按天轮转
    file_handler = TimedRotatingFileHandler(
        filename=os.path.join(settings.LOG_DIR, f"{name}.log"),
        when="midnight",
        interval=1,
        backupCount=30,
        encoding="utf-8"
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    
    # 格式化器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 创建应用日志器
app_logger = setup_logger("lark_robot")
