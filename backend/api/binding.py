"""绑定关系API路由"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from database import get_db
from schemas.server import UserServerBindingCreate, UserServerBindingResponse
from services.server_service import ServerService
from models.server import Server
from models.binding import UserServerBinding
from api.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=List[UserServerBindingResponse])
async def get_bindings(
    lark_user_id: Optional[str] = Query(None, description="Lark用户ID"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取绑定关系列表"""
    if lark_user_id:
        bindings = ServerService.get_user_bindings(db, lark_user_id)
    else:
        # 如果没有指定用户ID，返回所有绑定关系（管理员功能）
        bindings = db.query(UserServerBinding).all()
    
    return bindings

@router.post("/", response_model=UserServerBindingResponse)
async def create_binding(
    binding_data: UserServerBindingCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """创建绑定关系"""
    # 检查服务器是否存在
    server = db.query(Server).filter(Server.id == binding_data.server_id).first()
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="服务器不存在"
        )
    
    binding = ServerService.create_user_server_binding(
        db, binding_data.lark_user_id, binding_data.server_id
    )
    return binding

@router.delete("/{binding_id}")
async def delete_binding(
    binding_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """删除绑定关系"""
    success = ServerService.delete_user_server_binding(db, binding_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="绑定关系不存在"
        )
    
    return {"message": "绑定关系已删除"}
