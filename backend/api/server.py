"""服务器API路由"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional

from database import get_db
from schemas.server import ServerListResponse, ServerResponse
from services.server_service import ServerService
from models.server import Server
from api.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=ServerListResponse)
async def get_servers(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取服务器列表"""
    servers, total = ServerService.get_servers(db, skip=skip, limit=limit)
    return {
        "total": total,
        "items": servers
    }

@router.post("/sync")
async def sync_servers(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """手动同步服务器信息"""
    synced_count = await ServerService.sync_servers_from_vultr(db)
    return {
        "message": f"成功同步 {synced_count} 台服务器信息",
        "synced_count": synced_count
    }

@router.get("/{server_id}", response_model=ServerResponse)
async def get_server(
    server_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取单个服务器信息"""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="服务器不存在"
        )
    return server
