# 环境变量配置示例文件
# 复制此文件为 .env 并填入实际的配置值

# ===================
# 基础配置
# ===================
ENVIRONMENT=production
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===================
# 数据库配置
# ===================
# MySQL (生产环境推荐)
DATABASE_URL=mysql+pymysql://lark_user:lark_pass@mysql:3306/lark_robot
MYSQL_ROOT_PASSWORD=root123
MYSQL_DATABASE=lark_robot
MYSQL_USER=lark_user
MYSQL_PASSWORD=lark_pass

# SQLite (开发测试用，取消注释使用)
# DATABASE_URL=sqlite:///./data/sql_app.db

# ===================
# Redis配置
# ===================
REDIS_URL=redis://redis:6379/0

# ===================
# Lark配置
# ===================
# 从Lark开发者后台获取
LARK_APP_ID=your_lark_app_id
LARK_APP_SECRET=your_lark_app_secret
LARK_ENCRYPT_KEY=your_lark_encrypt_key
LARK_VERIFY_TOKEN=your_lark_verify_token

# ===================
# Vultr配置
# ===================
# 从Vultr控制台获取API密钥
VULTR_API_KEY=your_vultr_api_key

# ===================
# 服务端口配置
# ===================
BACKEND_PORT=8000
FRONTEND_PORT=80
REDIS_PORT=6379
MYSQL_PORT=3306

# ===================
# 日志配置
# ===================
LOG_LEVEL=INFO
LOG_FORMAT=json

# ===================
# Celery配置
# ===================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_WORKER_CONCURRENCY=2

# ===================
# 安全配置
# ===================
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://localhost:80

# ===================
# 监控配置
# ===================
ENABLE_METRICS=true
METRICS_PORT=9090
