#!/bin/bash

# Lark机器人系统Docker部署脚本

set -e  # 遇到错误时退出

echo "🚀 开始部署Lark机器人系统..."

# 检查Docker和docker-compose是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装，请先安装docker-compose"
    exit 1
fi

# 检查.env文件是否存在
if [ ! -f .env ]; then
    echo "⚠️  .env文件不存在，正在从env.example创建..."
    cp env.example .env
    echo "📝 请编辑.env文件并填入正确的配置值"
    echo "⚠️  特别注意配置以下必需的环境变量："
    echo "   - LARK_APP_ID"
    echo "   - LARK_APP_SECRET" 
    echo "   - LARK_ENCRYPT_KEY"
    echo "   - LARK_VERIFY_TOKEN"
    echo "   - VULTR_API_KEY"
    echo "   - SECRET_KEY"
    read -p "配置完成后按回车继续..."
fi

# 停止现有的容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理旧的镜像（可选）
read -p "是否要清理旧的Docker镜像？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
    docker image prune -f
fi

# 构建并启动服务
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🩺 进行健康检查..."

# 检查MySQL
if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD:-root123} &> /dev/null; then
    echo "✅ MySQL数据库服务正常"
else
    echo "❌ MySQL数据库服务异常"
fi

# 检查Redis
if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis服务正常"
else
    echo "❌ Redis服务异常"
fi

# 检查后端
if curl -f http://localhost:8000/health &> /dev/null; then
    echo "✅ 后端API服务正常"
else
    echo "❌ 后端API服务异常"
fi

# 检查前端
if curl -f http://localhost/health &> /dev/null; then
    echo "✅ 前端Web服务正常"
else
    echo "❌ 前端Web服务异常"
fi

# 检查Celery Worker
if docker-compose exec -T celery_worker celery -A tasks.celery_app inspect ping &> /dev/null; then
    echo "✅ Celery Worker服务正常"
else
    echo "❌ Celery Worker服务异常"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务访问地址："
echo "   前端Web界面: http://localhost"
echo "   后端API文档: http://localhost:8000/docs"
echo "   MySQL数据库: localhost:3306"
echo "   Redis缓存: localhost:6379"
echo ""
echo "🔐 默认登录信息："
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "📊 查看服务状态: docker-compose ps"
echo "📝 查看日志: docker-compose logs -f [service_name]"
echo "🛑 停止服务: docker-compose down"
echo ""

# 显示容器状态
echo "📊 当前容器状态："
docker-compose ps
