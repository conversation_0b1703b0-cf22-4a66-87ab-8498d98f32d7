# Lark机器人服务器重启系统

一个基于FastAPI和Lark机器人的服务器自动重启系统，支持通过Lark消息重启Vultr裸机服务器。

## 功能特性

- 🤖 **Lark机器人集成**: 通过@机器人或私聊发送IP/ID重启服务器
- 🖥️ **Vultr服务器管理**: 自动同步和管理Vultr裸机实例
- 👥 **用户权限管理**: 基于Lark用户ID的服务器绑定权限控制
- 📊 **Web管理界面**: 现代化的前端管理界面
- 📝 **完整日志记录**: 所有操作的详细日志记录
- ⚡ **异步任务处理**: 基于Celery的后台任务队列
- 🔐 **JWT认证**: 安全的API访问控制

## 技术栈

### 后端
- **FastAPI**: 高性能异步Web框架
- **SQLAlchemy**: ORM数据库操作
- **MySQL**: 主数据库
- **Celery + Redis**: 异步任务队列
- **Alembic**: 数据库迁移工具

### 前端
- **原生JavaScript**: 轻量级前端实现
- **Bootstrap 5**: 现代化UI组件库
- **Bootstrap Icons**: 图标库

## 项目结构

```
lark_robot/
├── backend/                   # 后端Python代码
│   ├── models/               # 数据模型
│   ├── schemas/              # Pydantic模型
│   ├── api/                  # API路由
│   ├── services/             # 业务逻辑
│   ├── tasks/                # Celery任务
│   ├── utils/                # 工具函数
│   ├── alembic/              # 数据库迁移
│   └── requirements.txt      # 依赖包
├── frontend/                 # 前端代码
│   └── src/
│       ├── pages/           # HTML页面
│       ├── js/              # JavaScript文件
│       ├── css/             # 样式文件
│       └── assets/          # 静态资源
└── README.md
```

## 快速开始

### 1. 环境准备

确保已安装：
- Python 3.8+
- MySQL 8.0+
- Redis 6.0+

### 2. 后端设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\\Scripts\\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入实际配置
vim .env
```

### 3. 数据库初始化

```bash
# 初始化Alembic
alembic init alembic

# 生成初始迁移
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head
```

### 4. 启动服务

```bash
# 启动FastAPI应用
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 启动Celery worker (新终端)
python start_celery.py worker

# 启动Celery beat (新终端)
python start_celery.py beat
```

### 5. 前端设置

```bash
# 进入前端目录
cd frontend

# 启动开发服务器
npm run dev
# 或使用Python
python -m http.server 8080 --directory src
```

访问 http://localhost:8080 查看前端管理界面。

## 配置说明

### 环境变量配置 (.env)

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/lark_robot

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Vultr API配置
VULTR_API_KEY=your-vultr-api-key
VULTR_API_BASE_URL=https://api.vultr.com

# Lark配置
LARK_APP_ID=your-lark-app-id
LARK_APP_SECRET=your-lark-app-secret
LARK_ENCRYPT_KEY=your-lark-encrypt-key
LARK_VERIFY_TOKEN=your-lark-verify-token
```

### Lark机器人配置

1. 在Lark开放平台创建应用
2. 配置机器人权限和事件订阅
3. 设置Webhook URL: `https://your-domain.com/webhook/lark`
4. 将应用信息填入.env文件

### Vultr API配置

1. 登录Vultr控制台
2. 生成API Key
3. 将API Key填入.env文件

## 使用说明

### Lark机器人使用

1. **重启服务器**: 在群里@机器人或私聊机器人，发送服务器IP或Vultr ID
2. **支持格式**:
   - IP地址: `*************`
   - Vultr ID: `12345678-1234-1234-1234-123456789012`

### Web管理界面

1. **登录系统**: 使用管理员账号登录
2. **服务器管理**: 查看和同步Vultr服务器列表
3. **绑定关系**: 管理Lark用户与服务器的绑定关系
4. **操作日志**: 查看所有操作的详细日志

## API文档

启动后端服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 部署建议

### 生产环境部署

1. **使用Gunicorn运行FastAPI**:
```bash
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

2. **使用Supervisor管理Celery**:
```ini
[program:celery_worker]
command=/path/to/venv/bin/celery -A backend.tasks.celery_app worker --loglevel=info
directory=/path/to/lark_robot/backend
user=your_user
autostart=true
autorestart=true

[program:celery_beat]
command=/path/to/venv/bin/celery -A backend.tasks.celery_app beat --loglevel=info
directory=/path/to/lark_robot/backend
user=your_user
autostart=true
autorestart=true
```

3. **使用Nginx作为反向代理**:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        root /path/to/lark_robot/frontend/src;
        try_files $uri $uri/ /index.html;
    }
}
```

## 开发指南

### 添加新功能

1. 在`models/`中定义数据模型
2. 在`schemas/`中定义Pydantic模型
3. 在`services/`中实现业务逻辑
4. 在`api/`中添加API路由
5. 运行数据库迁移

### 代码规范

```bash
# 代码格式化
black .
isort .

# 代码检查
flake8 .

# 运行测试
pytest
```

## 故障排除

### 常见问题

1. **数据库连接失败**: 检查MySQL服务状态和连接配置
2. **Redis连接失败**: 检查Redis服务状态
3. **Lark Webhook验证失败**: 检查LARK_ENCRYPT_KEY配置
4. **Vultr API调用失败**: 检查VULTR_API_KEY是否正确

### 日志查看

```bash
# 查看应用日志
tail -f logs/lark_robot.log

# 查看Celery日志
tail -f celery.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请提交Issue或联系项目维护者。
