# 部署检查清单

## 环境准备

### 系统要求
- [ ] Python 3.8+ 已安装
- [ ] MySQL 8.0+ 已安装并运行
- [ ] Redis 6.0+ 已安装并运行
- [ ] Nginx 已安装（生产环境）

### Python环境
- [ ] 创建虚拟环境
- [ ] 安装项目依赖 `pip install -r backend/requirements.txt`

## 配置文件

### 环境变量配置
- [ ] 复制 `backend/.env.example` 到 `backend/.env`
- [ ] 配置数据库连接 `DATABASE_URL`
- [ ] 设置JWT密钥 `JWT_SECRET_KEY`
- [ ] 配置Redis连接 `REDIS_URL`
- [ ] 设置Vultr API密钥 `VULTR_API_KEY`
- [ ] 配置Lark应用信息
  - [ ] `LARK_APP_ID`
  - [ ] `LARK_APP_SECRET`
  - [ ] `LARK_ENCRYPT_KEY`
  - [ ] `LARK_VERIFY_TOKEN`

### 数据库设置
- [ ] 创建数据库 `lark_robot`
- [ ] 数据库表结构会在应用启动时自动创建
- [ ] 创建管理员用户 `python backend/init_admin.py`

## Lark机器人配置

### 开放平台设置
- [ ] 创建Lark应用
- [ ] 开通机器人功能
- [ ] 配置机器人权限：
  - [ ] 接收消息
  - [ ] 发送消息
  - [ ] 获取用户信息
- [ ] 设置事件订阅：
  - [ ] 接收消息事件
  - [ ] 配置Webhook URL: `https://your-domain.com/webhook/lark`
- [ ] 发布应用到企业

### 权限配置
- [ ] 将机器人添加到需要的群组
- [ ] 测试机器人响应

## 服务启动

### 后端服务
- [ ] 启动FastAPI应用
  ```bash
  cd backend
  uvicorn main:app --host 0.0.0.0 --port 8000
  ```

### Celery服务
- [ ] 启动Celery Worker
  ```bash
  cd backend
  python start_celery.py worker
  ```
- [ ] 启动Celery Beat
  ```bash
  cd backend
  python start_celery.py beat
  ```

### 前端服务
- [ ] 启动前端服务器
  ```bash
  cd frontend
  npm run dev
  ```

## 功能测试

### API测试
- [ ] 访问API文档 `http://localhost:8000/docs`
- [ ] 测试用户登录接口
- [ ] 测试服务器同步接口

### Web界面测试
- [ ] 访问管理界面 `http://localhost:8080`
- [ ] 测试用户登录
- [ ] 测试服务器列表显示
- [ ] 测试绑定关系管理
- [ ] 测试操作日志查看

### 机器人测试
- [ ] 在Lark中@机器人发送测试消息
- [ ] 验证机器人响应
- [ ] 测试重启功能（使用测试服务器）

### 定时任务测试
- [ ] 验证服务器同步任务正常运行
- [ ] 检查Celery监控界面（如果启用）

## 生产环境部署

### 安全设置
- [ ] 更改默认密码
- [ ] 设置强JWT密钥
- [ ] 配置HTTPS
- [ ] 设置防火墙规则

### 服务配置
- [ ] 使用Gunicorn运行FastAPI
- [ ] 配置Supervisor管理Celery
- [ ] 配置Nginx反向代理
- [ ] 设置SSL证书

### 监控配置
- [ ] 配置日志轮转
- [ ] 设置监控告警
- [ ] 配置健康检查

### 备份配置
- [ ] 数据库备份策略
- [ ] 配置文件备份
- [ ] 日志备份策略

## 故障排除

### 常见问题检查
- [ ] 检查服务状态
  - [ ] MySQL服务运行正常
  - [ ] Redis服务运行正常
  - [ ] FastAPI应用运行正常
  - [ ] Celery服务运行正常

- [ ] 检查网络连接
  - [ ] Vultr API连接正常
  - [ ] Lark API连接正常
  - [ ] 数据库连接正常

- [ ] 检查日志文件
  - [ ] 应用日志无错误
  - [ ] Celery日志无错误
  - [ ] Nginx日志无错误（生产环境）

### 性能检查
- [ ] 数据库查询性能
- [ ] API响应时间
- [ ] 内存使用情况
- [ ] CPU使用情况

## 验收测试

### 功能验收
- [ ] 用户可以通过Lark机器人重启服务器
- [ ] 权限控制正常工作
- [ ] Web管理界面功能完整
- [ ] 操作日志记录完整
- [ ] 定时同步功能正常

### 性能验收
- [ ] 支持并发用户访问
- [ ] API响应时间符合要求
- [ ] 系统资源使用合理

### 安全验收
- [ ] 用户认证正常
- [ ] 权限控制有效
- [ ] 敏感信息加密存储
- [ ] API访问安全

## 上线清单

- [ ] 所有测试通过
- [ ] 文档更新完整
- [ ] 监控配置就绪
- [ ] 备份策略实施
- [ ] 用户培训完成
- [ ] 应急预案制定

## 维护计划

- [ ] 定期备份计划
- [ ] 系统更新计划
- [ ] 性能监控计划
- [ ] 安全审计计划
