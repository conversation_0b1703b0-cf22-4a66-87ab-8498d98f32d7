# 背景说明
- 为了提高重启服务器的效率，减少人工操作，现希望通过lark机器人自动操作重启服务器。

# 开发语言
- python
- fastapi
- celery
- sqlalchemy
- alembic

# 数据库
- mysql 8.0

# 核心流程
- 要有一个单独的定时任务来获取服务器列表，使用的是Vultr，列出裸机实例接口，数据要能入mysql数据库
- 用户通过在群里@lark机器人或者单聊lark机器人，发送服务器IP或ID来重启服务器。
- lark机器人接收到用户输入后：
  - 验证用户输入是否为服务器IP或ID。
  - 如果是，继续下一步；如果不是，提示用户输入正确的服务器IP或ID。
  - 如果用户输入的是IP，需要映射成ID，如果没有匹配的ID，提示用户输入正确的服务器IP或ID。
  - 如果用户输入的是ID，需要验证用户larkid是否有该机器ID的权限，如果没有权限，提示用户没有权限。
- 如果用户有权限，调用Vultr的重启接口重启服务器。
- 调用完成后，需要回复用户机器正在重启中。
- 这个时候系统需要每个10s，轮询调用Vultr获取裸机接口查询服务器重启状态，轮询6次。
- 如果6次轮询都没有查询到重启完成，需要回复用户重启失败。
- 如果查询到重启完成，需要回复用户重启成功。
- 重启完成后，需要记录用户这次操作日志以及服务器状态到数据库中。
- 上面凡是失败的情况，都需要记录日志到数据库中。
- 日志内容包括：用户larkid、服务器IP或ID、操作类型（重启）、操作状态（成功/失败）、操作时间、操作结果（如果失败，需要记录失败原因）

# 功能说明
- 定时Job，上线前初始化跑批获取所有裸机列表数据并入库，后期定期更新数据，频率支持配置文件配置
- lark账号与服务器关系首次绑定（基于用户首次发送重启消息），绑定关系账号与服务器1:n关系
- lark账号与服务器关系绑定维护页面（新增关系/修改与删除），支持1对多的账号绑定
  - 1、通过邮箱查询当前维护的lark用户id与服务器关系列表功能
  - 2、增加lark用户id与服务器关系功能
  - 3、修改lark用户id与服务器关系功能
  - 4、删除lark用户id与服务器关系功能
- lark机器人消息回复场景：
  - 1、输入指令错误回复：
    没看懂呢，请发送正确的远程电脑IP或机器ID，我能够帮助您重启远程电脑哦
  - 2、执行重启回复：
    您的远程电脑正在重启中，稍微休息一下，等待重启完成吧
  - 3、重启完成：
    您的远程电脑重启完成啦
  - 4、重启异常（尝试3次无法重启完成）：
    您的远程电脑重启失败，请联系管理员（记录异常日志）
  - 5、非法操作（重启别人的服务器）：
    您的远程电脑重启失败，请发送自己的远程电脑IP或机器ID（记录异常日志）
- 重启远程机器服务	
  - 绑定关系校验，只有绑定正确关系的用户才能执行重启服务，如果是首次重启服务，不校验
  - 重启过程中，裸机状态跟踪，如果重启失败，需要retry6次
  - 重启服务器过程日志入库
  - 重启服务器过程中，需要记录日志到数据库中，包括用户larkid、服务器IP或ID、操作类型（重启）、操作状态（成功/失败）、操作时间、操作结果（如果失败，需要记录失败原因）

# 对接三方接口
- Vultr
  - 列出裸机实例接口
  - 重启裸机实例接口
  - 获取裸机实例接口
- Lark
  - 订阅事件接口
  - 回复消息接口
  - 通过手机号或邮箱获取用户ID接口

# 相关敏感信息需要配置到.env文件里面
- Vultr API KEY
- Lark APP ID
- Lark APP SECRET
- Lark APP ENCRYPT_KEY
- Lark APP VERIFY_TOKEN