# Docker Compose配置文件
version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: lark_robot_redis
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - lark_robot_network

  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: lark_robot_mysql
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-lark_robot}
      MYSQL_USER: ${MYSQL_USER:-lark_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-lark_pass}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - lark_robot_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: lark_robot_backend
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    ports:
      - "8000:8000"
    environment: &backend_env
      # 数据库配置 (使用MySQL)
      DATABASE_URL: mysql+pymysql://${MYSQL_USER:-lark_user}:${MYSQL_PASSWORD:-lark_pass}@mysql:3306/${MYSQL_DATABASE:-lark_robot}
      
      # Redis配置
      REDIS_URL: redis://redis:6379/0
      
      # JWT配置
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      ALGORITHM: ${ALGORITHM:-HS256}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      
      # Lark配置
      LARK_APP_ID: ${LARK_APP_ID}
      LARK_APP_SECRET: ${LARK_APP_SECRET}
      LARK_ENCRYPT_KEY: ${LARK_ENCRYPT_KEY}
      LARK_VERIFY_TOKEN: ${LARK_VERIFY_TOKEN}
      
      # Vultr配置
      VULTR_API_KEY: ${VULTR_API_KEY}
      
      # 环境配置
      ENVIRONMENT: ${ENVIRONMENT:-production}
      TZ: Asia/Shanghai
    volumes:
      - backend_data:/app/data
      - ./backend/logs:/app/logs
    depends_on:
      - redis
      - mysql
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - lark_robot_network

  # Celery Worker服务
  celery_worker:
    image: lark_robot-backend
    container_name: lark_robot_celery_worker
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    environment: *backend_env
    volumes:
      - backend_data:/app/data
      - ./backend/logs:/app/logs
    depends_on:
      - redis
      - mysql
      - backend
    command: ["celery", "-A", "tasks.celery_app", "worker", "--loglevel=info", "--concurrency=2"]
    healthcheck:
      test: ["CMD", "celery", "-A", "tasks.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - lark_robot_network

  # Celery Beat调度器服务
  celery_beat:
    image: lark_robot-backend
    container_name: lark_robot_celery_beat
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    environment: *backend_env
    volumes:
      - backend_data:/app/data
      - ./backend/logs:/app/logs
    depends_on:
      - redis
      - mysql
      - backend
    command: ["celery", "-A", "tasks.celery_app", "beat", "--loglevel=info"]
    healthcheck:
      test: ["CMD", "celery", "-A", "tasks.celery_app", "inspect", "active"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - lark_robot_network

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: lark_robot_frontend
    restart: unless-stopped
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - lark_robot_network

# 数据卷
volumes:
  redis_data:
    driver: local
  backend_data:
    driver: local
  mysql_data:
    driver: local

# 网络
networks:
  lark_robot_network:
    driver: bridge
